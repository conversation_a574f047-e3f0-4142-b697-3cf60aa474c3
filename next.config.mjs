/** @type {import('next').NextConfig} */
const nextConfig = {
  // Add API proxy to avoid CORS issues (works in both local development and Vercel deployment)
  async rewrites() {
    // Enable proxy for local development and Vercel deployment
    // Only disable in production when using a different backend setup
    const shouldEnableProxy =
      process.env.NODE_ENV === "development" ||
      process.env.VERCEL === "1" ||
      process.env.NEXT_PUBLIC_API_URL?.includes("localhost:3000");

    return shouldEnableProxy
      ? [
          {
            source: "/api/v1/:path*",
            destination:
              "https://psai-api-wispy-resonance-3660.fly.dev/v1/:path*",
          },
        ]
      : [];
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.(".svg")
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ["@svgr/webpack"],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

export default nextConfig;
