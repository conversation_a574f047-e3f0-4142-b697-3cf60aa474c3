# Frontend API URL - REQUIRED for the app to function
# For local development (uses proxy to avoid CORS):
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# For Vercel deployment (direct backend connection):
# NEXT_PUBLIC_API_URL=https://your-backend-url.com/v1

# Backend URL for proxy (used in local development only)
# This should point to your actual backend server
PROXY_BACKEND_URL=https://your-backend-url.com

# Alternative: You can also use NEXT_PUBLIC_BACKEND_URL if you prefer
# NEXT_PUBLIC_BACKEND_URL=https://your-backend-url.com

# Development environment flag
NODE_ENV=development

# Optional: Sentry for error tracking
SENTRY_AUTH_TOKEN=